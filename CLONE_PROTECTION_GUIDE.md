# CloneEntity类型转换保护指南

## 问题描述

在Tensura模组生态系统中，经常出现 `ClassCastException` 错误，特别是当附属模组尝试将 `CloneEntity`（克隆实体）强制转换为 `Player`（玩家）类型时。

### 常见错误示例：
```
java.lang.ClassCastException: com.github.manasmods.tensura.entity.human.CloneEntity cannot be cast to net.minecraft.world.entity.player.Player
```

## 解决方案

本模组提供了完整的CloneEntity类型转换保护系统，包括：

### 1. 自动保护（Mixin）
- 自动拦截 `SkillHelper` 类中的危险类型转换
- 无需修改其他模组代码
- 透明地防止 `ClassCastException`

### 2. 安全工具类
为其他模组开发者提供安全的类型转换方法。

## 使用方法

### 对于模组用户
只需安装本模组，保护功能会自动生效。

### 对于模组开发者

#### 1. 安全的Player类型检查
```java
import com.github.b4ndithelps.tensuraskilldisabled.handler.CloneEntityProtectionHandler;

// 不安全的方式（可能抛出ClassCastException）
if (entity instanceof Player) {
    Player player = (Player) entity; // 危险！
}

// 安全的方式
if (CloneEntityProtectionHandler.canCastToPlayer(entity)) {
    Player player = CloneEntityProtectionHandler.safePlayerCast(entity);
    // 安全使用player
}
```

#### 2. 使用通用安全转换器
```java
import com.github.b4ndithelps.tensuraskilldisabled.handler.ClassCastExceptionPreventer;

// 安全的Player转换
Player player = ClassCastExceptionPreventer.safeAsPlayer(entity);
if (player != null) {
    // 安全使用player
}

// 安全执行Player操作
ClassCastExceptionPreventer.safePlayerOperation(entity, 
    player -> player.getDisplayName().getString(), 
    "Unknown Player"
);

// 安全执行Player动作
ClassCastExceptionPreventer.safePlayerAction(entity, 
    player -> player.sendSystemMessage(Component.literal("Hello!"))
);
```

#### 3. 安全的资源操作
```java
// 安全获取魔素值
double magicule = CloneEntityProtectionHandler.SafeCastUtils.getSafeMagicule(entity);

// 安全消耗魔素
boolean success = CloneEntityProtectionHandler.SafeCastUtils.safeMagiculeConsume(entity, 100.0);

// 安全获取光环值
double aura = CloneEntityProtectionHandler.SafeCastUtils.getSafeAura(entity);
```

## 保护范围

### 自动保护的方法：
- `SkillHelper.getAP()`
- `SkillHelper.outOfAura()`
- `SkillHelper.outOfEachEP()`
- `SkillHelper.getMP()`
- `SkillHelper.outOfMagicule()`

### 手动保护建议：
对于其他可能出现类型转换问题的地方，建议使用提供的安全工具类。

## 技术实现

### Mixin拦截
```java
@Redirect(method = "getAP", at = @At(value = "CHECKCAST", target = "net/minecraft/world/entity/player/Player"))
private static Player redirectPlayerCast(Object entity) {
    if (entity instanceof LivingEntity) {
        return CloneEntityProtectionHandler.safePlayerCast((LivingEntity) entity);
    }
    return null;
}
```

### 安全检查逻辑
```java
public static boolean isCloneEntity(LivingEntity entity) {
    if (CLONE_ENTITY_CLASS == null) {
        return false;
    }
    return CLONE_ENTITY_CLASS.isAssignableFrom(entity.getClass());
}
```

## 兼容性

- ✅ 与所有Tensura附属模组兼容
- ✅ 不影响正常的Player操作
- ✅ 对CloneEntity提供适当的替代处理
- ✅ 向后兼容，不破坏现有功能

## 调试信息

启用调试日志可以查看保护系统的工作情况：
```
[INFO] CloneEntity protection activated for entity: CloneEntity['Clone'/123, l='ServerLevel[world]', x=100.0, y=64.0, z=200.0]
[DEBUG] Safe cast failed: CloneEntity to Player
```

## 常见问题

### Q: 为什么CloneEntity不能转换为Player？
A: CloneEntity虽然看起来像玩家，但它是一个独立的实体类型，不继承自Player类。强制转换会导致ClassCastException。

### Q: 这会影响游戏性能吗？
A: 影响极小。保护检查使用缓存机制，只在第一次检查时进行反射操作。

### Q: 如何为我的模组添加保护？
A: 使用提供的静态方法替换直接的类型转换，或者通过反射调用保护方法。

## 示例代码

### 修复前（危险）：
```java
public void someSkillMethod(LivingEntity entity) {
    if (entity instanceof Player) {
        Player player = (Player) entity; // 可能抛出ClassCastException
        // 使用player...
    }
}
```

### 修复后（安全）：
```java
public void someSkillMethod(LivingEntity entity) {
    Player player = ClassCastExceptionPreventer.safeAsPlayer(entity);
    if (player != null) {
        // 安全使用player...
    }
}
```

## 贡献

如果发现新的类型转换问题，请提交Issue并提供：
1. 错误堆栈跟踪
2. 触发条件
3. 相关模组版本信息

我们会及时添加相应的保护措施。
