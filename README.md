# Tensura Skill Disabled Mod

一个用于转生史莱姆模组的技能禁用系统，允许服务器管理员阻止玩家获得特定技能。

## 功能特性

- 🚫 **技能黑名单系统** - 阻止玩家获得指定的技能
- ⚙️ **配置文件管理** - 通过JSON配置文件管理禁用技能列表
- 🎮 **游戏内命令** - 提供完整的命令系统管理黑名单
- 🌐 **多语言支持** - 支持中文和英文
- 🔄 **热重载** - 支持运行时重新加载配置
- 🖥️ **服务端可选** - 只需在服务端安装，客户端可选
- 🔧 **栈溢出修复** - 内置Tensura模组栈溢出问题修复
- 🔍 **登录检查** - 玩家登录时自动检查并移除黑名单技能
- 📦 **批量模组管理** - 一键禁用/解禁整个模组的所有技能
- 🎯 **技能模式禁用** - 精确禁用技能的特定模式，支持Tab补全
- 🛡️ **CloneEntity保护** - 防止附属模组的ClassCastException错误

## 安装方法

### **服务端安装（推荐）**
1. 确保服务端已安装以下前置模组：
   - Minecraft Forge 1.19.2-43.4.6+
   - ManasCore
   - Tensura (转生史莱姆主模组)

2. 将本模组文件放入服务端的 `mods` 文件夹

3. 启动服务器

### **客户端安装（可选）**
- **客户端不需要安装此模组**
- 玩家可以正常连接服务器，无需下载额外模组
- 如果客户端也安装了，不会有任何问题

## 配置文件

模组会在 `config/skill_blacklist.json` 创建配置文件：

```json
{
  "version": "1.0",
  "description": "Tensura Skill Blacklist Configuration - List skills that players cannot obtain",
  "disabledSkills": [
    "tensura:gluttony",
    "tensura:predator",
    "tensura:degenerate"
  ]
}
```

### 配置说明

- `disabledSkills`: 禁用技能列表，使用技能的注册名（格式：`namespace:path`）

## 命令使用

所有命令需要OP权限（权限等级2）：

### 基础命令
```
/skillblacklist
```
显示帮助信息

### 单个技能管理
```
/skillblacklist add <skill_id>        # 添加技能到黑名单
/skillblacklist remove <skill_id>     # 从黑名单移除技能
```
例如：
- `/skillblacklist add tensura:gluttony`
- `/skillblacklist remove tensura:gluttony`

### 批量模组管理 🆕
```
/skillblacklist addmod <mod_id>       # 禁用整个模组的所有技能
/skillblacklist removemod <mod_id>    # 解禁整个模组的所有技能
```
例如：
- `/skillblacklist addmod tensura` - 禁用Tensura模组的所有技能
- `/skillblacklist removemod tensura` - 解禁Tensura模组的所有技能

### 查看和管理
```
/skillblacklist list                  # 列出所有黑名单技能
/skillblacklist listmods              # 列出所有有技能的模组
/skillblacklist reload                # 重新加载配置
```

## 技能ID获取方法

要获取技能的正确ID，可以：

1. 查看Tensura模组的源代码或文档
2. 使用F3+H显示高级工具提示，查看技能物品的ID
3. 查看游戏日志，当技能被阻止时会显示技能ID

## 常见技能ID示例

```
tensura:gluttony          - 暴食者
tensura:predator          - 捕食者
tensura:degenerate        - 堕落者
tensura:sage              - 贤者
tensura:great_sage        - 大贤者
tensura:raphael           - 拉斐尔
tensura:uriel             - 乌列尔
tensura:gabriel           - 加百列
tensura:michael           - 米迦勒
```

## 工作原理

1. **事件监听**: 模组监听 `UnlockSkillEvent` 事件
2. **黑名单检查**: 当玩家尝试获得技能时，检查技能是否在黑名单中
3. **静默阻止**: 如果技能在黑名单中，静默取消事件（无提示、无日志）
4. **登录检查**: 玩家登录时自动扫描并移除已有的黑名单技能
5. **配置管理**: 提供命令和配置文件管理黑名单

## CloneEntity保护系统

本模组内置了CloneEntity（克隆实体）的类型转换保护系统，防止附属模组出现ClassCastException错误。

### 常见问题
许多Tensura附属模组会尝试将CloneEntity强制转换为Player类型，导致崩溃：
```
ClassCastException: CloneEntity cannot be cast to Player
```

### 自动保护
- 🛡️ **Mixin拦截** - 自动拦截SkillHelper中的危险类型转换
- 🔧 **安全转换** - 提供安全的类型转换方法
- 📋 **兼容性** - 与所有Tensura附属模组兼容
- 🚫 **零配置** - 无需额外设置，自动生效

### 保护范围
自动保护以下方法中的类型转换：
- `SkillHelper.getAP()`
- `SkillHelper.outOfAura()`
- `SkillHelper.outOfEachEP()`
- `SkillHelper.getMP()`
- `SkillHelper.outOfMagicule()`

详细信息请参考 [CloneEntity保护指南](CLONE_PROTECTION_GUIDE.md)

## 开发信息

- **Minecraft版本**: 1.19.2
- **Forge版本**: 43.4.6+
- **开发语言**: Java
- **许可证**: MIT

## 更新日志

### v1.0.0
- 初始版本
- 基础技能黑名单功能
- 配置文件系统
- 命令管理系统
- 多语言支持

## 支持与反馈

如果遇到问题或有建议，请：
1. 检查配置文件格式是否正确
2. 确认技能ID格式正确（namespace:path）
3. 查看游戏日志获取详细错误信息

## 注意事项

- 技能ID必须使用正确的格式：`namespace:path`
- 配置文件修改后需要使用 `/skillblacklist reload` 命令重新加载
- 只有OP玩家可以使用管理命令
- **自动清理**: 玩家登录时会自动移除已有的黑名单技能
- **静默模式**: 技能被阻止时不会有任何提示消息或日志记录
- **服务端专用**: 此模组设计为服务端专用，客户端无需安装

## Credits

基于 Tensura: Reincarnated 模组开发
Check out the Tensura: Reincarnated mod here: https://www.curseforge.com/minecraft/mc-mods/tensura-reincarnated
