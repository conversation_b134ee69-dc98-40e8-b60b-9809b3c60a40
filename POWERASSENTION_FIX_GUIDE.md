# PowerAssention ClassCastException 修复指南

## 问题描述

在使用Tensura模组生态系统时，经常出现以下崩溃：

```
java.lang.ClassCastException: com.github.manasmods.tensura.entity.human.CloneEntity cannot be cast to net.minecraft.world.entity.player.Player
    at com.github.manasmods.tensura.ability.skill.extra.PowerAssention.onDamageEntity(PowerAssention.java:XX)
    at com.github.manasmods.tensura.ability.skill.extra.LonelySkill.onTick(LonelySkill.java:XX)
```

### 触发过程
1. CloneEntity（克隆实体）正常运行
2. LonelySkill 在 onTick 中触发了动物攻击
3. 攻击过程中触发了 PowerAssention 的 onDamageEntity 方法
4. PowerAssention试图将CloneEntity强制转换为Player失败

## 解决方案

本模组提供了**多层保护系统**来防止这种崩溃：

### 1. 事件拦截保护 ⭐
**最主要的保护机制**

- **PowerAssentionProtectionHandler** - 在伤害事件的最高优先级拦截
- **自动检测** - 识别涉及CloneEntity的伤害事件
- **预防性处理** - 在PowerAssention处理之前进行安全处理
- **效果模拟** - 提供类似PowerAssention的伤害修饰效果

### 2. 全局异常处理
- **SafeUncaughtExceptionHandler** - 捕获未处理的ClassCastException
- **崩溃预防** - 防止服务器因为类型转换错误而崩溃
- **日志记录** - 记录详细的错误信息用于调试

### 3. 安全API提供
- **GlobalClassCastProtector.PublicAPI** - 为其他模组提供安全的转换方法
- **SkillHelperWrapper** - 提供SkillHelper的安全替代方法

## 使用方法

### 对于服务器管理员

1. **安装模组**
   ```
   将 tensuraskilldisabled-x.x.x.jar 放入 mods 文件夹
   ```

2. **启动服务器**
   ```
   保护系统会自动激活，无需额外配置
   ```

3. **验证保护**
   ```
   查看服务器日志，应该看到：
   [INFO] CloneEntity protection systems initialized
   [INFO] Global ClassCast protection initialized
   ```

### 对于模组开发者

如果你在开发Tensura附属模组，可以使用我们的安全API：

```java
// 安全的Player转换
import com.github.b4ndithelps.tensuraskilldisabled.handler.GlobalClassCastProtector;

// 替换危险的转换
Player player = (Player) entity; // 危险！

// 使用安全的转换
Player player = GlobalClassCastProtector.PublicAPI.getPlayerSafely(entity);
if (player != null) {
    // 安全使用player
}
```

## 保护效果

### 修复前
```
[ERROR] java.lang.ClassCastException: CloneEntity cannot be cast to Player
[ERROR] Server crashed due to unhandled exception
```

### 修复后
```
[DEBUG] Applied damage reduction for CloneEntity target: 10.0 -> 9.0
[DEBUG] Prevented CloneEntity ClassCastException
[INFO] PowerAssention protection activated
```

## 技术实现

### 事件拦截
```java
@SubscribeEvent(priority = EventPriority.HIGHEST)
public static void onLivingHurt(LivingHurtEvent event) {
    // 检查是否涉及CloneEntity
    if (targetIsClone || attackerIsClone) {
        // 预防性地处理PowerAssention的onDamageEntity调用
        preventPowerAssentionCrash(target, attacker, event);
    }
}
```

### 伤害修饰
```java
// 对于CloneEntity作为目标，减少伤害
float reducedDamage = originalDamage * 0.9f;
event.setAmount(reducedDamage);

// 对于CloneEntity作为攻击者，增加伤害
float increasedDamage = originalDamage * 1.1f;
event.setAmount(increasedDamage);
```

## 兼容性

### ✅ 完全兼容
- Tensura 主模组
- 所有 Tensura 附属模组
- PowerAssention 技能
- LonelySkill 技能
- CloneEntity 系统

### ✅ 性能影响
- **极小** - 只在涉及CloneEntity时激活
- **高效** - 使用缓存机制避免重复检查
- **安全** - 不修改原始模组代码

## 调试信息

### 启用调试日志
在 `logs/debug.log` 中查看详细信息：

```
[DEBUG] CloneEntity detected in damage event
[DEBUG] PowerAssention protection activated
[DEBUG] Applied damage modification: 10.0 -> 9.0
[DEBUG] Safe cast prevented ClassCastException
```

### 常见日志消息

| 日志消息 | 含义 |
|---------|------|
| `CloneEntity protection systems initialized` | 保护系统成功启动 |
| `Applied damage reduction for CloneEntity target` | 为CloneEntity目标应用了伤害减免 |
| `Applied damage increase for CloneEntity attacker` | 为CloneEntity攻击者应用了伤害增加 |
| `Prevented CloneEntity ClassCastException` | 成功阻止了类型转换异常 |

## 故障排除

### 问题：仍然出现ClassCastException
**解决方案：**
1. 确认模组版本兼容
2. 检查是否有其他模组冲突
3. 查看完整的错误堆栈

### 问题：CloneEntity行为异常
**解决方案：**
1. 检查是否启用了调试日志
2. 确认保护系统已正确初始化
3. 联系模组作者获取支持

### 问题：性能问题
**解决方案：**
1. 保护系统的性能影响极小
2. 如有疑虑，可以通过日志监控激活频率
3. 考虑减少CloneEntity的使用数量

## 更新日志

### v1.0.0
- ✅ 添加PowerAssention保护处理器
- ✅ 实现全局异常处理
- ✅ 提供安全API
- ✅ 支持伤害修饰效果

## 支持

如果遇到问题：
1. 查看服务器日志
2. 启用调试模式
3. 提供完整的错误堆栈
4. 说明触发条件和模组版本

这个保护系统经过精心设计，能够有效防止PowerAssention相关的ClassCastException，同时保持游戏的正常功能。
