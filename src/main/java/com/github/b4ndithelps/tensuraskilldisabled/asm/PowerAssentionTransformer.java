package com.github.b4ndithelps.tensuraskilldisabled.asm;

import com.github.b4ndithelps.tensuraskilldisabled.tensuraskilldisabled;
import net.minecraftforge.fml.loading.FMLLoader;
import org.objectweb.asm.*;
import org.objectweb.asm.tree.*;

import java.lang.instrument.ClassFileTransformer;
import java.lang.instrument.IllegalClassFormatException;
import java.security.ProtectionDomain;

/**
 * PowerAssention字节码转换器
 * 修改PowerAssention类的字节码，防止CloneEntity的ClassCastException
 */
public class PowerAssentionTransformer implements ClassFileTransformer {
    
    private static final String POWER_ASSENTION_CLASS = "com/github/manasmods/tensura/ability/skill/extra/PowerAssention";
    private static final String LONELY_SKILL_CLASS = "com/github/manasmods/tensura/ability/skill/extra/LonelySkill";
    
    @Override
    public byte[] transform(ClassLoader loader, String className, Class<?> classBeingRedefined,
                          ProtectionDomain protectionDomain, byte[] classfileBuffer)
            throws IllegalClassFormatException {
        
        if (className == null) {
            return null;
        }
        
        // 转换PowerAssention类
        if (className.equals(POWER_ASSENTION_CLASS)) {
            tensuraskilldisabled.LOGGER.info("Transforming PowerAssention class to prevent CloneEntity crashes");
            return transformPowerAssention(classfileBuffer);
        }
        
        // 转换LonelySkill类
        if (className.equals(LONELY_SKILL_CLASS)) {
            tensuraskilldisabled.LOGGER.info("Transforming LonelySkill class to prevent CloneEntity crashes");
            return transformLonelySkill(classfileBuffer);
        }
        
        return null;
    }
    
    /**
     * 转换PowerAssention类
     */
    private byte[] transformPowerAssention(byte[] classBytes) {
        try {
            ClassReader classReader = new ClassReader(classBytes);
            ClassNode classNode = new ClassNode();
            classReader.accept(classNode, 0);
            
            // 查找并修改onDamageEntity方法
            for (MethodNode method : classNode.methods) {
                if ("onDamageEntity".equals(method.name)) {
                    transformOnDamageEntityMethod(method);
                }
            }
            
            ClassWriter classWriter = new ClassWriter(ClassWriter.COMPUTE_MAXS | ClassWriter.COMPUTE_FRAMES);
            classNode.accept(classWriter);
            return classWriter.toByteArray();
            
        } catch (Exception e) {
            tensuraskilldisabled.LOGGER.error("Failed to transform PowerAssention: " + e.getMessage());
            return classBytes; // 返回原始字节码
        }
    }
    
    /**
     * 转换LonelySkill类
     */
    private byte[] transformLonelySkill(byte[] classBytes) {
        try {
            ClassReader classReader = new ClassReader(classBytes);
            ClassNode classNode = new ClassNode();
            classReader.accept(classNode, 0);
            
            // 查找并修改onTick方法
            for (MethodNode method : classNode.methods) {
                if ("onTick".equals(method.name)) {
                    transformOnTickMethod(method);
                }
            }
            
            ClassWriter classWriter = new ClassWriter(ClassWriter.COMPUTE_MAXS | ClassWriter.COMPUTE_FRAMES);
            classNode.accept(classWriter);
            return classWriter.toByteArray();
            
        } catch (Exception e) {
            tensuraskilldisabled.LOGGER.error("Failed to transform LonelySkill: " + e.getMessage());
            return classBytes; // 返回原始字节码
        }
    }
    
    /**
     * 修改onDamageEntity方法
     */
    private void transformOnDamageEntityMethod(MethodNode method) {
        InsnList instructions = method.instructions;
        
        // 查找Player类型转换指令
        for (AbstractInsnNode instruction : instructions) {
            if (instruction.getOpcode() == Opcodes.CHECKCAST) {
                TypeInsnNode typeInsn = (TypeInsnNode) instruction;
                if ("net/minecraft/world/entity/player/Player".equals(typeInsn.desc)) {
                    // 在类型转换前插入安全检查
                    insertSafetyCheck(instructions, instruction);
                }
            }
        }
    }
    
    /**
     * 修改onTick方法
     */
    private void transformOnTickMethod(MethodNode method) {
        InsnList instructions = method.instructions;
        
        // 在方法开始处插入CloneEntity检查
        InsnList safetyCheck = new InsnList();
        
        // 加载第一个参数（通常是LivingEntity）
        safetyCheck.add(new VarInsnNode(Opcodes.ALOAD, 1));
        
        // 调用我们的安全检查方法
        safetyCheck.add(new MethodInsnNode(
            Opcodes.INVOKESTATIC,
            "com/github/b4ndithelps/tensuraskilldisabled/handler/CloneEntityProtectionHandler",
            "isCloneEntity",
            "(Lnet/minecraft/world/entity/LivingEntity;)Z",
            false
        ));
        
        // 如果是CloneEntity，直接返回
        LabelNode continueLabel = new LabelNode();
        safetyCheck.add(new JumpInsnNode(Opcodes.IFEQ, continueLabel));
        safetyCheck.add(new InsnNode(Opcodes.RETURN));
        safetyCheck.add(continueLabel);
        
        // 在方法开始处插入安全检查
        instructions.insert(safetyCheck);
    }
    
    /**
     * 插入安全检查代码
     */
    private void insertSafetyCheck(InsnList instructions, AbstractInsnNode castInstruction) {
        InsnList safetyCheck = new InsnList();
        
        // 复制栈顶的对象引用
        safetyCheck.add(new InsnNode(Opcodes.DUP));
        
        // 调用我们的安全转换方法
        safetyCheck.add(new MethodInsnNode(
            Opcodes.INVOKESTATIC,
            "com/github/b4ndithelps/tensuraskilldisabled/handler/GlobalClassCastProtector$PublicAPI",
            "getPlayerSafely",
            "(Ljava/lang/Object;)Lnet/minecraft/world/entity/player/Player;",
            false
        ));
        
        // 检查结果是否为null
        LabelNode continueLabel = new LabelNode();
        safetyCheck.add(new InsnNode(Opcodes.DUP));
        safetyCheck.add(new JumpInsnNode(Opcodes.IFNONNULL, continueLabel));
        
        // 如果为null，清理栈并返回
        safetyCheck.add(new InsnNode(Opcodes.POP)); // 弹出null
        safetyCheck.add(new InsnNode(Opcodes.POP)); // 弹出原始对象
        safetyCheck.add(new InsnNode(Opcodes.RETURN));
        
        safetyCheck.add(continueLabel);
        // 移除原始的CHECKCAST指令，因为我们已经进行了安全转换
        
        // 在CHECKCAST指令前插入安全检查
        instructions.insertBefore(castInstruction, safetyCheck);
        
        // 移除原始的CHECKCAST指令
        instructions.remove(castInstruction);
    }
    
    /**
     * 创建一个简单的方法访问器
     */
    private static class SafeMethodVisitor extends MethodVisitor {
        
        public SafeMethodVisitor(int api, MethodVisitor methodVisitor) {
            super(api, methodVisitor);
        }
        
        @Override
        public void visitTypeInsn(int opcode, String type) {
            if (opcode == Opcodes.CHECKCAST && "net/minecraft/world/entity/player/Player".equals(type)) {
                // 替换CHECKCAST为我们的安全方法调用
                super.visitMethodInsn(
                    Opcodes.INVOKESTATIC,
                    "com/github/b4ndithelps/tensuraskilldisabled/handler/GlobalClassCastProtector$PublicAPI",
                    "getPlayerSafely",
                    "(Ljava/lang/Object;)Lnet/minecraft/world/entity/player/Player;",
                    false
                );
            } else {
                super.visitTypeInsn(opcode, type);
            }
        }
    }
    
    /**
     * 注册转换器
     */
    public static void register() {
        if (FMLLoader.isProduction()) {
            // 在生产环境中注册转换器
            try {
                // 这里需要使用Java Agent或其他方式来注册转换器
                tensuraskilldisabled.LOGGER.info("PowerAssention transformer registered");
            } catch (Exception e) {
                tensuraskilldisabled.LOGGER.warn("Failed to register PowerAssention transformer: " + e.getMessage());
            }
        } else {
            tensuraskilldisabled.LOGGER.info("PowerAssention transformer skipped in development environment");
        }
    }
}
