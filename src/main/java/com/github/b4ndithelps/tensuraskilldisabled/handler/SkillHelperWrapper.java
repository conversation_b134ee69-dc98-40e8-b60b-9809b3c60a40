package com.github.b4ndithelps.tensuraskilldisabled.handler;

import com.github.b4ndithelps.tensuraskilldisabled.tensuraskilldisabled;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;

import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;

/**
 * SkillHelper的安全包装器
 * 提供安全的方法调用，防止CloneEntity的ClassCastException
 */
public class SkillHelperWrapper {
    
    private static final ConcurrentHashMap<String, Method> METHOD_CACHE = new ConcurrentHashMap<>();
    private static Class<?> SKILL_HELPER_CLASS = null;
    
    static {
        try {
            SKILL_HELPER_CLASS = Class.forName("com.github.manasmods.tensura.ability.SkillHelper");
        } catch (ClassNotFoundException e) {
            tensuraskilldisabled.LOGGER.warn("SkillHelper class not found, wrapper disabled");
        }
    }
    
    /**
     * 安全获取AP值
     */
    public static double safeGetAP(LivingEntity entity, boolean max) {
        if (CloneEntityProtectionHandler.isCloneEntity(entity)) {
            return CloneEntityProtectionHandler.SafeCastUtils.getSafeAura(entity);
        }
        
        return callOriginalGetAP(entity, max);
    }
    
    /**
     * 安全获取MP值
     */
    public static double safeGetMP(LivingEntity entity, boolean max) {
        if (CloneEntityProtectionHandler.isCloneEntity(entity)) {
            return CloneEntityProtectionHandler.SafeCastUtils.getSafeMagicule(entity);
        }
        
        return callOriginalGetMP(entity, max);
    }
    
    /**
     * 安全检查是否缺少光环
     */
    public static boolean safeOutOfAura(LivingEntity entity, double cost) {
        if (CloneEntityProtectionHandler.isCloneEntity(entity)) {
            return !CloneEntityProtectionHandler.SafeCastUtils.safeMagiculeConsume(entity, cost);
        }
        
        return callOriginalOutOfAura(entity, cost);
    }
    
    /**
     * 安全检查是否缺少魔素
     */
    public static boolean safeOutOfMagicule(LivingEntity entity, double cost) {
        if (CloneEntityProtectionHandler.isCloneEntity(entity)) {
            return !CloneEntityProtectionHandler.SafeCastUtils.safeMagiculeConsume(entity, cost);
        }
        
        return callOriginalOutOfMagicule(entity, cost);
    }
    
    /**
     * 安全检查是否缺少双重EP
     */
    public static boolean safeOutOfEachEP(LivingEntity entity, double cost) {
        if (CloneEntityProtectionHandler.isCloneEntity(entity)) {
            return !CloneEntityProtectionHandler.SafeCastUtils.safeMagiculeConsume(entity, cost * 2.0);
        }
        
        return callOriginalOutOfEachEP(entity, cost);
    }
    
    // 原始方法调用
    
    private static double callOriginalGetAP(LivingEntity entity, boolean max) {
        try {
            Method method = getMethod("getAP", LivingEntity.class, boolean.class);
            if (method != null) {
                return (Double) method.invoke(null, entity, max);
            }
        } catch (Exception e) {
            tensuraskilldisabled.LOGGER.debug("Failed to call original getAP: " + e.getMessage());
        }
        return 0.0;
    }
    
    private static double callOriginalGetMP(LivingEntity entity, boolean max) {
        try {
            Method method = getMethod("getMP", LivingEntity.class, boolean.class);
            if (method != null) {
                return (Double) method.invoke(null, entity, max);
            }
        } catch (Exception e) {
            tensuraskilldisabled.LOGGER.debug("Failed to call original getMP: " + e.getMessage());
        }
        return 0.0;
    }
    
    private static boolean callOriginalOutOfAura(LivingEntity entity, double cost) {
        try {
            Method method = getMethod("outOfAura", LivingEntity.class, double.class);
            if (method != null) {
                return (Boolean) method.invoke(null, entity, cost);
            }
        } catch (Exception e) {
            tensuraskilldisabled.LOGGER.debug("Failed to call original outOfAura: " + e.getMessage());
        }
        return true; // 安全默认值
    }
    
    private static boolean callOriginalOutOfMagicule(LivingEntity entity, double cost) {
        try {
            Method method = getMethod("outOfMagicule", LivingEntity.class, double.class);
            if (method != null) {
                return (Boolean) method.invoke(null, entity, cost);
            }
        } catch (Exception e) {
            tensuraskilldisabled.LOGGER.debug("Failed to call original outOfMagicule: " + e.getMessage());
        }
        return true; // 安全默认值
    }
    
    private static boolean callOriginalOutOfEachEP(LivingEntity entity, double cost) {
        try {
            Method method = getMethod("outOfEachEP", LivingEntity.class, double.class);
            if (method != null) {
                return (Boolean) method.invoke(null, entity, cost);
            }
        } catch (Exception e) {
            tensuraskilldisabled.LOGGER.debug("Failed to call original outOfEachEP: " + e.getMessage());
        }
        return true; // 安全默认值
    }
    
    private static Method getMethod(String methodName, Class<?>... paramTypes) {
        if (SKILL_HELPER_CLASS == null) {
            return null;
        }
        
        String cacheKey = methodName + "_" + paramTypes.length;
        return METHOD_CACHE.computeIfAbsent(cacheKey, k -> {
            try {
                Method method = SKILL_HELPER_CLASS.getMethod(methodName, paramTypes);
                method.setAccessible(true);
                return method;
            } catch (NoSuchMethodException e) {
                tensuraskilldisabled.LOGGER.debug("Method not found: " + methodName);
                return null;
            }
        });
    }
    
    /**
     * 检查是否可以安全调用SkillHelper方法
     */
    public static boolean isSkillHelperAvailable() {
        return SKILL_HELPER_CLASS != null;
    }
    
    /**
     * 为其他模组提供的静态访问方法
     * 其他模组可以通过反射调用这些方法来避免ClassCastException
     */
    public static class PublicAPI {
        
        /**
         * 安全的Player类型检查
         */
        public static boolean canSafelyUseAsPlayer(LivingEntity entity) {
            return CloneEntityProtectionHandler.canCastToPlayer(entity);
        }
        
        /**
         * 安全的Player转换
         */
        public static Player safeAsPlayer(LivingEntity entity) {
            return CloneEntityProtectionHandler.safePlayerCast(entity);
        }
        
        /**
         * 检查是否为CloneEntity
         */
        public static boolean isCloneEntity(LivingEntity entity) {
            return CloneEntityProtectionHandler.isCloneEntity(entity);
        }
        
        /**
         * 安全获取实体的魔素值
         */
        public static double getSafeMagicule(LivingEntity entity) {
            return CloneEntityProtectionHandler.SafeCastUtils.getSafeMagicule(entity);
        }
        
        /**
         * 安全获取实体的光环值
         */
        public static double getSafeAura(LivingEntity entity) {
            return CloneEntityProtectionHandler.SafeCastUtils.getSafeAura(entity);
        }
        
        /**
         * 安全消耗魔素
         */
        public static boolean safeMagiculeConsume(LivingEntity entity, double cost) {
            return CloneEntityProtectionHandler.SafeCastUtils.safeMagiculeConsume(entity, cost);
        }
    }
}
