package com.github.b4ndithelps.tensuraskilldisabled.handler;

import com.github.b4ndithelps.tensuraskilldisabled.tensuraskilldisabled;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * CloneEntity类型转换保护处理器
 * 防止其他技能将CloneEntity强制转换为Player导致ClassCastException
 */
@Mod.EventBusSubscriber(modid = tensuraskilldisabled.MODID, bus = Mod.EventBusSubscriber.Bus.FORGE)
public class CloneEntityProtectionHandler {
    
    // 缓存CloneEntity类的引用
    private static Class<?> CLONE_ENTITY_CLASS = null;
    
    // 缓存已检查的类，避免重复反射
    private static final Map<Class<?>, Boolean> CLASS_CACHE = new ConcurrentHashMap<>();
    
    // 记录已经修复的方法，避免重复处理
    private static final Map<String, Boolean> PATCHED_METHODS = new ConcurrentHashMap<>();
    
    static {
        try {
            // 尝试加载CloneEntity类
            CLONE_ENTITY_CLASS = Class.forName("com.github.manasmods.tensura.entity.human.CloneEntity");
        } catch (ClassNotFoundException e) {
            // 如果找不到CloneEntity类，说明Tensura模组版本不同或未安装
            tensuraskilldisabled.LOGGER.warn("CloneEntity class not found, clone protection disabled");
        }
    }
    
    /**
     * 检查实体是否为CloneEntity
     */
    public static boolean isCloneEntity(LivingEntity entity) {
        if (CLONE_ENTITY_CLASS == null) {
            return false;
        }
        
        Class<?> entityClass = entity.getClass();
        
        // 使用缓存避免重复检查
        return CLASS_CACHE.computeIfAbsent(entityClass, clazz -> 
            CLONE_ENTITY_CLASS.isAssignableFrom(clazz)
        );
    }
    
    /**
     * 安全的Player类型转换
     * 如果是CloneEntity，返回null而不是抛出异常
     */
    public static Player safePlayerCast(LivingEntity entity) {
        if (entity instanceof Player && !isCloneEntity(entity)) {
            return (Player) entity;
        }
        return null;
    }
    
    /**
     * 检查实体是否可以安全转换为Player
     */
    public static boolean canCastToPlayer(LivingEntity entity) {
        return entity instanceof Player && !isCloneEntity(entity);
    }
    
    /**
     * 监听伤害事件，在技能处理之前进行保护
     */
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onLivingHurt(LivingHurtEvent event) {
        LivingEntity target = event.getEntity();
        LivingEntity attacker = null;
        
        if (event.getSource().getEntity() instanceof LivingEntity) {
            attacker = (LivingEntity) event.getSource().getEntity();
        }
        
        // 如果攻击者或目标是CloneEntity，进行保护性检查
        if ((attacker != null && isCloneEntity(attacker)) || isCloneEntity(target)) {
            // 这里可以添加特定的保护逻辑
            // 目前主要是通过静态方法提供安全的类型转换
        }
    }
    
    /**
     * 为技能系统提供的安全工具方法
     */
    public static class SafeCastUtils {
        
        /**
         * 安全获取玩家的魔素值
         */
        public static double getSafeMagicule(LivingEntity entity) {
            if (canCastToPlayer(entity)) {
                try {
                    Player player = (Player) entity;
                    // 使用反射调用TensuraPlayerCapability.getMagicule
                    Class<?> capabilityClass = Class.forName("com.github.manasmods.tensura.capability.race.TensuraPlayerCapability");
                    Method getMagiculeMethod = capabilityClass.getMethod("getMagicule", Player.class);
                    return (Double) getMagiculeMethod.invoke(null, player);
                } catch (Exception e) {
                    // 如果反射失败，返回默认值
                    return 0.0;
                }
            } else {
                // 对于非玩家实体（包括CloneEntity），使用EP系统
                try {
                    Class<?> epCapabilityClass = Class.forName("com.github.manasmods.tensura.capability.ep.TensuraEPCapability");
                    Method getEPMethod = epCapabilityClass.getMethod("getEP", LivingEntity.class);
                    return (Double) getEPMethod.invoke(null, entity);
                } catch (Exception e) {
                    return 0.0;
                }
            }
        }
        
        /**
         * 安全获取玩家的光环值
         */
        public static double getSafeAura(LivingEntity entity) {
            if (canCastToPlayer(entity)) {
                try {
                    Player player = (Player) entity;
                    Class<?> capabilityClass = Class.forName("com.github.manasmods.tensura.capability.race.TensuraPlayerCapability");
                    Method getAuraMethod = capabilityClass.getMethod("getAura", Player.class);
                    return (Double) getAuraMethod.invoke(null, player);
                } catch (Exception e) {
                    return 0.0;
                }
            } else {
                // 对于非玩家实体，使用EP作为替代
                return getSafeMagicule(entity);
            }
        }
        
        /**
         * 安全消耗魔素
         */
        public static boolean safeMagiculeConsume(LivingEntity entity, double cost) {
            if (canCastToPlayer(entity)) {
                try {
                    Player player = (Player) entity;
                    
                    // 检查是否有足够的魔素
                    double currentMagicule = getSafeMagicule(entity);
                    if (currentMagicule < cost) {
                        return false;
                    }
                    
                    // 消耗魔素
                    Class<?> capabilityClass = Class.forName("com.github.manasmods.tensura.capability.race.TensuraPlayerCapability");
                    Method decreaseMethod = capabilityClass.getMethod("decreaseMagicule", Player.class, double.class);
                    decreaseMethod.invoke(null, player, cost);
                    return true;
                } catch (Exception e) {
                    return false;
                }
            } else {
                // 对于非玩家实体，使用EP系统
                try {
                    Class<?> epCapabilityClass = Class.forName("com.github.manasmods.tensura.capability.ep.TensuraEPCapability");
                    
                    // 检查当前EP
                    Method getCurrentEPMethod = epCapabilityClass.getMethod("getCurrentEP", LivingEntity.class);
                    double currentEP = (Double) getCurrentEPMethod.invoke(null, entity);
                    
                    if (currentEP < cost) {
                        return false;
                    }
                    
                    // 消耗EP
                    Method decreaseEPMethod = epCapabilityClass.getMethod("decreaseCurrentEP", LivingEntity.class, double.class);
                    decreaseEPMethod.invoke(null, entity, cost);
                    return true;
                } catch (Exception e) {
                    return false;
                }
            }
        }
        
        /**
         * 检查实体是否为创造模式玩家
         */
        public static boolean isCreativePlayer(LivingEntity entity) {
            if (canCastToPlayer(entity)) {
                Player player = (Player) entity;
                return player.isCreative();
            }
            return false;
        }
        
        /**
         * 安全发送消息给玩家
         */
        public static void safeSendMessage(LivingEntity entity, Object message) {
            if (canCastToPlayer(entity)) {
                try {
                    Player player = (Player) entity;
                    if (message instanceof net.minecraft.network.chat.Component) {
                        player.sendSystemMessage((net.minecraft.network.chat.Component) message);
                    }
                } catch (Exception e) {
                    // 静默处理异常
                }
            }
            // 对于非玩家实体，不发送消息
        }
    }
}
