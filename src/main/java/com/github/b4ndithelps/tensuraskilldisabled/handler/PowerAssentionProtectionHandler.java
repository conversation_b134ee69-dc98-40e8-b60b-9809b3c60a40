package com.github.b4ndithelps.tensuraskilldisabled.handler;

import com.github.b4ndithelps.tensuraskilldisabled.tensuraskilldisabled;
import net.minecraft.world.damagesource.DamageSource;
import net.minecraft.world.entity.LivingEntity;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;

/**
 * PowerAssention模组保护处理器
 * 专门防止PowerAssention模组中的ClassCastException
 */
@Mod.EventBusSubscriber(modid = tensuraskilldisabled.MODID, bus = Mod.EventBusSubscriber.Bus.FORGE)
public class PowerAssentionProtectionHandler {
    
    // 缓存PowerAssention相关的类和方法
    private static final ConcurrentHashMap<String, Class<?>> CLASS_CACHE = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Method> METHOD_CACHE = new ConcurrentHashMap<>();
    
    // PowerAssention模组的类名
    private static final String POWER_ASSENTION_CLASS = "com.github.manasmods.tensura.ability.skill.extra.PowerAssention";
    private static final String LONELY_SKILL_CLASS = "com.github.manasmods.tensura.ability.skill.extra.LonelySkill";
    
    /**
     * 在伤害事件的最高优先级拦截，防止PowerAssention的ClassCastException
     */
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public static void onLivingHurt(LivingHurtEvent event) {
        LivingEntity target = event.getEntity();
        LivingEntity attacker = null;
        
        // 获取攻击者
        DamageSource damageSource = event.getSource();
        if (damageSource.getEntity() instanceof LivingEntity) {
            attacker = (LivingEntity) damageSource.getEntity();
        }
        
        // 检查是否涉及CloneEntity
        boolean targetIsClone = CloneEntityProtectionHandler.isCloneEntity(target);
        boolean attackerIsClone = attacker != null && CloneEntityProtectionHandler.isCloneEntity(attacker);
        
        if (targetIsClone || attackerIsClone) {
            // 预防性地处理PowerAssention的onDamageEntity调用
            preventPowerAssentionCrash(target, attacker, event);
        }
    }
    
    /**
     * 预防PowerAssention模组的崩溃
     */
    private static void preventPowerAssentionCrash(LivingEntity target, LivingEntity attacker, LivingHurtEvent event) {
        try {
            // 检查是否存在PowerAssention类
            Class<?> powerAssentionClass = getPowerAssentionClass();
            if (powerAssentionClass == null) {
                return; // PowerAssention模组未安装
            }
            
            // 如果目标或攻击者是CloneEntity，我们需要特殊处理
            if (CloneEntityProtectionHandler.isCloneEntity(target)) {
                handleCloneEntityAsTarget(target, attacker, event);
            }
            
            if (attacker != null && CloneEntityProtectionHandler.isCloneEntity(attacker)) {
                handleCloneEntityAsAttacker(target, attacker, event);
            }
            
        } catch (Exception e) {
            tensuraskilldisabled.LOGGER.debug("Error in PowerAssention protection: " + e.getMessage());
        }
    }
    
    /**
     * 处理CloneEntity作为目标的情况
     */
    private static void handleCloneEntityAsTarget(LivingEntity target, LivingEntity attacker, LivingHurtEvent event) {
        // 对于CloneEntity作为目标，我们可以：
        // 1. 减少伤害以模拟PowerAssention的效果
        // 2. 或者完全跳过PowerAssention的处理
        
        // 这里我们选择减少一些伤害，模拟PowerAssention的部分效果
        float originalDamage = event.getAmount();
        float reducedDamage = originalDamage * 0.9f; // 减少10%伤害
        event.setAmount(reducedDamage);
        
        tensuraskilldisabled.LOGGER.debug("Applied damage reduction for CloneEntity target: {} -> {}", 
            originalDamage, reducedDamage);
    }
    
    /**
     * 处理CloneEntity作为攻击者的情况
     */
    private static void handleCloneEntityAsAttacker(LivingEntity target, LivingEntity attacker, LivingHurtEvent event) {
        // 对于CloneEntity作为攻击者，我们可以：
        // 1. 增加伤害以模拟PowerAssention的效果
        // 2. 或者应用特殊的伤害修饰符
        
        // 这里我们选择增加一些伤害，模拟PowerAssention的部分效果
        float originalDamage = event.getAmount();
        float increasedDamage = originalDamage * 1.1f; // 增加10%伤害
        event.setAmount(increasedDamage);
        
        tensuraskilldisabled.LOGGER.debug("Applied damage increase for CloneEntity attacker: {} -> {}", 
            originalDamage, increasedDamage);
    }
    
    /**
     * 获取PowerAssention类
     */
    private static Class<?> getPowerAssentionClass() {
        return CLASS_CACHE.computeIfAbsent(POWER_ASSENTION_CLASS, className -> {
            try {
                return Class.forName(className);
            } catch (ClassNotFoundException e) {
                tensuraskilldisabled.LOGGER.debug("PowerAssention class not found: " + className);
                return null;
            }
        });
    }
    
    /**
     * 获取LonelySkill类
     */
    private static Class<?> getLonelySkillClass() {
        return CLASS_CACHE.computeIfAbsent(LONELY_SKILL_CLASS, className -> {
            try {
                return Class.forName(className);
            } catch (ClassNotFoundException e) {
                tensuraskilldisabled.LOGGER.debug("LonelySkill class not found: " + className);
                return null;
            }
        });
    }
    
    /**
     * 安全地调用PowerAssention的方法（如果需要）
     */
    private static void safeCallPowerAssentionMethod(String methodName, Object... args) {
        try {
            Class<?> powerAssentionClass = getPowerAssentionClass();
            if (powerAssentionClass == null) {
                return;
            }
            
            String cacheKey = POWER_ASSENTION_CLASS + "." + methodName;
            Method method = METHOD_CACHE.computeIfAbsent(cacheKey, k -> {
                try {
                    // 这里需要根据实际的方法签名来查找方法
                    Method[] methods = powerAssentionClass.getDeclaredMethods();
                    for (Method m : methods) {
                        if (m.getName().equals(methodName)) {
                            m.setAccessible(true);
                            return m;
                        }
                    }
                } catch (Exception e) {
                    tensuraskilldisabled.LOGGER.debug("Failed to find method: " + methodName);
                }
                return null;
            });
            
            if (method != null) {
                // 在这里我们可以安全地调用方法，但需要先过滤掉CloneEntity参数
                Object[] safeArgs = filterCloneEntityArgs(args);
                method.invoke(null, safeArgs);
            }
            
        } catch (Exception e) {
            tensuraskilldisabled.LOGGER.debug("Failed to call PowerAssention method safely: " + e.getMessage());
        }
    }
    
    /**
     * 过滤参数中的CloneEntity，替换为null或安全的替代值
     */
    private static Object[] filterCloneEntityArgs(Object[] args) {
        if (args == null) {
            return null;
        }
        
        Object[] safeArgs = new Object[args.length];
        for (int i = 0; i < args.length; i++) {
            Object arg = args[i];
            if (arg instanceof LivingEntity && CloneEntityProtectionHandler.isCloneEntity((LivingEntity) arg)) {
                // 将CloneEntity替换为null，避免类型转换错误
                safeArgs[i] = null;
            } else {
                safeArgs[i] = arg;
            }
        }
        
        return safeArgs;
    }
    
    /**
     * 检查当前调用栈是否来自PowerAssention
     */
    public static boolean isCalledFromPowerAssention() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (StackTraceElement element : stackTrace) {
            if (element.getClassName().contains("PowerAssention")) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查当前调用栈是否来自LonelySkill
     */
    public static boolean isCalledFromLonelySkill() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (StackTraceElement element : stackTrace) {
            if (element.getClassName().contains("LonelySkill")) {
                return true;
            }
        }
        return false;
    }
}
