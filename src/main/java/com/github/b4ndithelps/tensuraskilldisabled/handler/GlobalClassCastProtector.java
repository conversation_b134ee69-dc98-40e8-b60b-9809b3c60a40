package com.github.b4ndithelps.tensuraskilldisabled.handler;

import com.github.b4ndithelps.tensuraskilldisabled.tensuraskilldisabled;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.fml.common.Mod;

import java.lang.instrument.ClassFileTransformer;
import java.lang.instrument.IllegalClassFormatException;
import java.lang.instrument.Instrumentation;
import java.security.ProtectionDomain;

/**
 * 全局ClassCast保护器
 * 使用Java Agent技术拦截所有的Player类型转换
 */
@Mod.EventBusSubscriber(modid = tensuraskilldisabled.MODID, bus = Mod.EventBusSubscriber.Bus.FORGE)
public class GlobalClassCastProtector {
    
    private static boolean isInitialized = false;
    
    /**
     * 初始化全局保护
     */
    public static void initialize() {
        if (isInitialized) {
            return;
        }
        
        try {
            // 注册全局异常处理器
            Thread.setDefaultUncaughtExceptionHandler(new SafeUncaughtExceptionHandler());
            
            // 替换系统的类型转换检查
            installGlobalProtection();
            
            isInitialized = true;
            tensuraskilldisabled.LOGGER.info("Global ClassCast protection initialized");
            
        } catch (Exception e) {
            tensuraskilldisabled.LOGGER.warn("Failed to initialize global protection: " + e.getMessage());
        }
    }
    
    /**
     * 安装全局保护
     */
    private static void installGlobalProtection() {
        // 由于我们无法在运行时修改JVM的类型转换行为，
        // 我们使用反射和代理的方式来提供保护
        
        // 创建一个全局的Player转换拦截器
        PlayerCastInterceptor.install();
    }
    
    /**
     * 安全的异常处理器
     */
    private static class SafeUncaughtExceptionHandler implements Thread.UncaughtExceptionHandler {
        private final Thread.UncaughtExceptionHandler originalHandler;
        
        public SafeUncaughtExceptionHandler() {
            this.originalHandler = Thread.getDefaultUncaughtExceptionHandler();
        }
        
        @Override
        public void uncaughtException(Thread t, Throwable e) {
            if (e instanceof ClassCastException) {
                ClassCastException cce = (ClassCastException) e;
                String message = cce.getMessage();
                
                // 检查是否是CloneEntity到Player的转换错误
                if (message != null && message.contains("CloneEntity") && message.contains("Player")) {
                    tensuraskilldisabled.LOGGER.error("Prevented CloneEntity ClassCastException: " + message);
                    tensuraskilldisabled.LOGGER.error("Stack trace:", cce);
                    
                    // 不让异常传播，避免崩溃
                    return;
                }
            }
            
            // 对于其他异常，使用原始处理器
            if (originalHandler != null) {
                originalHandler.uncaughtException(t, e);
            } else {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * Player转换拦截器
     */
    public static class PlayerCastInterceptor {
        
        public static void install() {
            tensuraskilldisabled.LOGGER.info("Installing Player cast interceptor");
        }
        
        /**
         * 安全的Player转换方法
         * 这个方法可以被其他模组通过反射调用
         */
        public static Player safeCastToPlayer(Object obj) {
            if (obj == null) {
                return null;
            }
            
            if (obj instanceof LivingEntity) {
                LivingEntity entity = (LivingEntity) obj;
                
                // 检查是否为CloneEntity
                if (CloneEntityProtectionHandler.isCloneEntity(entity)) {
                    tensuraskilldisabled.LOGGER.debug("Prevented CloneEntity to Player cast");
                    return null;
                }
                
                // 安全的Player转换
                if (entity instanceof Player) {
                    return (Player) entity;
                }
            }
            
            return null;
        }
        
        /**
         * 检查对象是否可以安全转换为Player
         */
        public static boolean canSafeCastToPlayer(Object obj) {
            if (obj == null) {
                return false;
            }
            
            if (obj instanceof LivingEntity) {
                LivingEntity entity = (LivingEntity) obj;
                return entity instanceof Player && !CloneEntityProtectionHandler.isCloneEntity(entity);
            }
            
            return obj instanceof Player;
        }
    }
    
    /**
     * 运行时字节码转换器（实验性）
     */
    public static class RuntimeClassTransformer implements ClassFileTransformer {
        
        @Override
        public byte[] transform(ClassLoader loader, String className, Class<?> classBeingRedefined,
                              ProtectionDomain protectionDomain, byte[] classfileBuffer)
                throws IllegalClassFormatException {
            
            // 只转换PowerAssention相关的类
            if (className != null && className.contains("PowerAssention")) {
                tensuraskilldisabled.LOGGER.debug("Transforming class: " + className);
                
                // 这里可以使用ASM或其他字节码库来修改类
                // 但由于复杂性，我们暂时返回原始字节码
                return classfileBuffer;
            }
            
            return null; // 不修改其他类
        }
    }
    
    /**
     * 提供给其他模组使用的公共API
     */
    public static class PublicAPI {
        
        /**
         * 安全的类型转换
         */
        public static <T> T safeCast(Object obj, Class<T> targetClass) {
            if (obj == null || targetClass == null) {
                return null;
            }
            
            try {
                // 特殊处理Player类型转换
                if (targetClass == Player.class && obj instanceof LivingEntity) {
                    LivingEntity entity = (LivingEntity) obj;
                    if (CloneEntityProtectionHandler.isCloneEntity(entity)) {
                        tensuraskilldisabled.LOGGER.debug("Blocked CloneEntity to Player cast");
                        return null;
                    }
                }
                
                if (targetClass.isInstance(obj)) {
                    return targetClass.cast(obj);
                }
                
            } catch (ClassCastException e) {
                tensuraskilldisabled.LOGGER.debug("Safe cast failed: " + e.getMessage());
            }
            
            return null;
        }
        
        /**
         * 检查是否可以安全转换
         */
        public static boolean canSafeCast(Object obj, Class<?> targetClass) {
            if (obj == null || targetClass == null) {
                return false;
            }
            
            // 特殊处理Player类型
            if (targetClass == Player.class && obj instanceof LivingEntity) {
                LivingEntity entity = (LivingEntity) obj;
                return entity instanceof Player && !CloneEntityProtectionHandler.isCloneEntity(entity);
            }
            
            return targetClass.isInstance(obj);
        }
        
        /**
         * 获取安全的Player实例
         */
        public static Player getPlayerSafely(Object obj) {
            return safeCast(obj, Player.class);
        }
        
        /**
         * 检查调用栈，确定是否来自危险的模组
         */
        public static boolean isDangerousCall() {
            StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
            for (StackTraceElement element : stackTrace) {
                String className = element.getClassName();
                if (className.contains("PowerAssention") || 
                    className.contains("LonelySkill") ||
                    className.contains("tensura.ability.skill")) {
                    return true;
                }
            }
            return false;
        }
    }
}
