package com.github.b4ndithelps.tensuraskilldisabled.handler;

import com.github.b4ndithelps.tensuraskilldisabled.tensuraskilldisabled;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraftforge.eventbus.api.EventPriority;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;

import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 通用的ClassCastException预防器
 * 提供静态方法供其他模组安全地进行类型转换
 */
@Mod.EventBusSubscriber(modid = tensuraskilldisabled.MODID, bus = Mod.EventBusSubscriber.Bus.MOD)
public class ClassCastExceptionPreventer {
    
    // 缓存反射方法调用
    private static final ConcurrentHashMap<String, Method> METHOD_CACHE = new ConcurrentHashMap<>();
    
    @SubscribeEvent
    public static void onCommonSetup(FMLCommonSetupEvent event) {
        tensuraskilldisabled.LOGGER.info("ClassCastException Preventer initialized");
    }
    
    /**
     * 安全的Player类型检查和转换
     * 这个方法可以被其他模组通过反射调用
     */
    public static Player safeAsPlayer(LivingEntity entity) {
        if (entity == null) {
            return null;
        }
        
        // 检查是否为CloneEntity
        if (CloneEntityProtectionHandler.isCloneEntity(entity)) {
            return null; // CloneEntity不能转换为Player
        }
        
        // 安全的类型转换
        if (entity instanceof Player) {
            return (Player) entity;
        }
        
        return null;
    }
    
    /**
     * 检查实体是否可以安全转换为Player
     */
    public static boolean canSafelyConvertToPlayer(LivingEntity entity) {
        if (entity == null) {
            return false;
        }
        
        return entity instanceof Player && !CloneEntityProtectionHandler.isCloneEntity(entity);
    }
    
    /**
     * 安全执行需要Player类型的操作
     */
    public static <T> T safePlayerOperation(LivingEntity entity, PlayerOperation<T> operation, T defaultValue) {
        Player player = safeAsPlayer(entity);
        if (player != null) {
            try {
                return operation.execute(player);
            } catch (Exception e) {
                tensuraskilldisabled.LOGGER.warn("Error in player operation: " + e.getMessage());
                return defaultValue;
            }
        }
        return defaultValue;
    }
    
    /**
     * 安全执行需要Player类型的操作（无返回值）
     */
    public static void safePlayerAction(LivingEntity entity, PlayerAction action) {
        Player player = safeAsPlayer(entity);
        if (player != null) {
            try {
                action.execute(player);
            } catch (Exception e) {
                tensuraskilldisabled.LOGGER.warn("Error in player action: " + e.getMessage());
            }
        }
    }
    
    /**
     * 获取实体的显示名称（安全版本）
     */
    public static String getSafeDisplayName(LivingEntity entity) {
        if (entity == null) {
            return "Unknown";
        }
        
        try {
            return entity.getDisplayName().getString();
        } catch (Exception e) {
            return entity.getClass().getSimpleName();
        }
    }
    
    /**
     * 安全获取实体的UUID字符串
     */
    public static String getSafeUUIDString(LivingEntity entity) {
        if (entity == null) {
            return "null";
        }
        
        try {
            return entity.getUUID().toString();
        } catch (Exception e) {
            return "unknown-uuid";
        }
    }
    
    /**
     * 检查实体是否为特定类型（使用类名字符串，避免直接类引用）
     */
    public static boolean isEntityOfType(LivingEntity entity, String className) {
        if (entity == null || className == null) {
            return false;
        }
        
        try {
            Class<?> targetClass = Class.forName(className);
            return targetClass.isInstance(entity);
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
    
    /**
     * 安全调用实体的方法（通过反射）
     */
    public static Object safeInvokeMethod(LivingEntity entity, String methodName, Class<?>[] paramTypes, Object... args) {
        if (entity == null || methodName == null) {
            return null;
        }
        
        try {
            String cacheKey = entity.getClass().getName() + "." + methodName;
            Method method = METHOD_CACHE.computeIfAbsent(cacheKey, k -> {
                try {
                    Method m = entity.getClass().getMethod(methodName, paramTypes);
                    m.setAccessible(true);
                    return m;
                } catch (NoSuchMethodException e) {
                    return null;
                }
            });
            
            if (method != null) {
                return method.invoke(entity, args);
            }
        } catch (Exception e) {
            tensuraskilldisabled.LOGGER.debug("Failed to invoke method {} on {}: {}", 
                methodName, entity.getClass().getSimpleName(), e.getMessage());
        }
        
        return null;
    }
    
    /**
     * 创建安全的类型转换包装器
     */
    public static <T> T safeCast(Object obj, Class<T> targetClass, T defaultValue) {
        if (obj == null || targetClass == null) {
            return defaultValue;
        }
        
        try {
            if (targetClass.isInstance(obj)) {
                // 特殊处理Player类型转换
                if (targetClass == Player.class && obj instanceof LivingEntity) {
                    LivingEntity entity = (LivingEntity) obj;
                    if (CloneEntityProtectionHandler.isCloneEntity(entity)) {
                        return defaultValue; // 不允许CloneEntity转换为Player
                    }
                }
                
                return targetClass.cast(obj);
            }
        } catch (ClassCastException e) {
            tensuraskilldisabled.LOGGER.debug("Safe cast failed: {} to {}", 
                obj.getClass().getSimpleName(), targetClass.getSimpleName());
        }
        
        return defaultValue;
    }
    
    /**
     * Player操作接口
     */
    @FunctionalInterface
    public interface PlayerOperation<T> {
        T execute(Player player) throws Exception;
    }
    
    /**
     * Player动作接口
     */
    @FunctionalInterface
    public interface PlayerAction {
        void execute(Player player) throws Exception;
    }
}
