package com.github.b4ndithelps.tensuraskilldisabled.handler;

import com.github.b4ndithelps.tensuraskilldisabled.config.SkillBlacklistConfig;
import com.github.b4ndithelps.tensuraskilldisabled.tensuraskilldisabled;
import com.github.manasmods.manascore.api.skills.ManasSkill;
import com.github.manasmods.manascore.api.skills.SkillAPI;
import com.github.manasmods.tensura.ability.skill.Skill;
import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;
import net.minecraft.world.item.Items;
import net.minecraftforge.event.server.ServerStartedEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 技能需求修改器
 * 为黑名单技能添加不可能完成的解锁条件
 */
@Mod.EventBusSubscriber(modid = tensuraskilldisabled.MODID, bus = Mod.EventBusSubscriber.Bus.FORGE)
public class SkillRequirementModifier {

    // 不可能的魔素要求
    private static final long IMPOSSIBLE_MAGICULE_REQUIREMENT = 2_000_000_000L;

    // 存储原始的meetEPRequirement方法
    private static final Map<Class<?>, Method> ORIGINAL_METHODS = new ConcurrentHashMap<>();
    
    /**
     * 检查玩家是否满足不可能的条件
     * 这个方法会被技能黑名单处理器调用
     */
    public static boolean hasImpossibleRequirements(Player player) {
        return hasImpossibleItems(player) && hasImpossibleMagicule(player);
    }

    /**
     * 检查玩家是否有不可能的物品组合
     */
    private static boolean hasImpossibleItems(Player player) {
        boolean hasBarrier = false;
        boolean hasBedrock = false;

        // 检查玩家背包中的物品
        for (ItemStack stack : player.getInventory().items) {
            if (stack.is(Items.BARRIER)) {
                hasBarrier = true;
            }
            if (stack.is(Items.BEDROCK)) {
                hasBedrock = true;
            }
        }

        return hasBarrier && hasBedrock;
    }

    /**
     * 检查玩家是否有不可能的魔素数量
     */
    private static boolean hasImpossibleMagicule(Player player) {
        try {
            // 使用反射调用TensuraPlayerCapability.getMagicule方法
            Class<?> capabilityClass = Class.forName("com.github.manasmods.tensura.capability.race.TensuraPlayerCapability");
            var method = capabilityClass.getMethod("getMagicule", Player.class);
            double currentMagicule = (Double) method.invoke(null, player);

            return currentMagicule >= IMPOSSIBLE_MAGICULE_REQUIREMENT;

        } catch (Exception e) {
            // 如果获取魔素失败，返回false
            return false;
        }
    }

    /**
     * 当配置重新加载时的占位方法
     */
    public static void onConfigReload() {
        // 这个方法现在不需要做任何事情
        // 因为我们改用事件拦截的方式
    }
}
