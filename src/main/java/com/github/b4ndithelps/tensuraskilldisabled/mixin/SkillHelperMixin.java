package com.github.b4ndithelps.tensuraskilldisabled.mixin;

import com.github.b4ndithelps.tensuraskilldisabled.handler.CloneEntityProtectionHandler;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

/**
 * SkillHelper类的Mixin，用于修复CloneEntity的类型转换问题
 * 使用更安全的方法拦截，避免直接修改字节码
 */
@Mixin(value = com.github.manasmods.tensura.ability.SkillHelper.class, remap = false)
public class SkillHelperMixin {

    /**
     * 拦截getAP方法，防止CloneEntity的类型转换错误
     */
    @Inject(method = "getAP", at = @At("HEAD"), cancellable = true)
    private static void onGetAP(LivingEntity entity, boolean max, CallbackInfoReturnable<Double> cir) {
        if (CloneEntityProtectionHandler.isCloneEntity(entity)) {
            // 对于CloneEntity，使用EP系统而不是Player系统
            double result = CloneEntityProtectionHandler.SafeCastUtils.getSafeAura(entity);
            cir.setReturnValue(result);
        }
    }

    /**
     * 拦截getMP方法，防止CloneEntity的类型转换错误
     */
    @Inject(method = "getMP", at = @At("HEAD"), cancellable = true)
    private static void onGetMP(LivingEntity entity, boolean max, CallbackInfoReturnable<Double> cir) {
        if (CloneEntityProtectionHandler.isCloneEntity(entity)) {
            // 对于CloneEntity，使用EP系统而不是Player系统
            double result = CloneEntityProtectionHandler.SafeCastUtils.getSafeMagicule(entity);
            cir.setReturnValue(result);
        }
    }

    /**
     * 拦截outOfAura方法，防止CloneEntity的类型转换错误
     */
    @Inject(method = "outOfAura(Lnet/minecraft/world/entity/LivingEntity;D)Z", at = @At("HEAD"), cancellable = true)
    private static void onOutOfAura(LivingEntity entity, double cost, CallbackInfoReturnable<Boolean> cir) {
        if (CloneEntityProtectionHandler.isCloneEntity(entity)) {
            // 对于CloneEntity，使用安全的消耗方法
            boolean result = !CloneEntityProtectionHandler.SafeCastUtils.safeMagiculeConsume(entity, cost);
            cir.setReturnValue(result);
        }
    }

    /**
     * 拦截outOfMagicule方法，防止CloneEntity的类型转换错误
     */
    @Inject(method = "outOfMagicule", at = @At("HEAD"), cancellable = true)
    private static void onOutOfMagicule(LivingEntity entity, double cost, CallbackInfoReturnable<Boolean> cir) {
        if (CloneEntityProtectionHandler.isCloneEntity(entity)) {
            // 对于CloneEntity，使用安全的消耗方法
            boolean result = !CloneEntityProtectionHandler.SafeCastUtils.safeMagiculeConsume(entity, cost);
            cir.setReturnValue(result);
        }
    }

    /**
     * 拦截outOfEachEP方法，防止CloneEntity的类型转换错误
     */
    @Inject(method = "outOfEachEP", at = @At("HEAD"), cancellable = true)
    private static void onOutOfEachEP(LivingEntity entity, double cost, CallbackInfoReturnable<Boolean> cir) {
        if (CloneEntityProtectionHandler.isCloneEntity(entity)) {
            // 对于CloneEntity，使用安全的消耗方法（消耗双倍EP）
            boolean result = !CloneEntityProtectionHandler.SafeCastUtils.safeMagiculeConsume(entity, cost * 2.0);
            cir.setReturnValue(result);
        }
    }
}
