package com.github.b4ndithelps.tensuraskilldisabled.mixin;

import com.github.b4ndithelps.tensuraskilldisabled.handler.CloneEntityProtectionHandler;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Redirect;

/**
 * SkillHelper类的Mixin，用于修复CloneEntity的类型转换问题
 * 这个Mixin会拦截SkillHelper中的Player类型转换，防止ClassCastException
 */
@Mixin(value = com.github.manasmods.tensura.ability.SkillHelper.class, remap = false)
public class SkillHelperMixin {
    
    /**
     * 拦截getAP方法中的Player类型转换
     */
    @Redirect(method = "getAP", at = @At(value = "INSTANCEOF", target = "net/minecraft/world/entity/player/Player"))
    private static boolean redirectPlayerInstanceCheck(Object entity) {
        if (entity instanceof LivingEntity) {
            return CloneEntityProtectionHandler.canCastToPlayer((LivingEntity) entity);
        }
        return entity instanceof Player;
    }
    
    /**
     * 拦截outOfAura方法中的Player类型转换
     */
    @Redirect(method = "outOfAura(Lnet/minecraft/world/entity/LivingEntity;D)Z", 
              at = @At(value = "CHECKCAST", target = "net/minecraft/world/entity/player/Player"))
    private static Player redirectPlayerCastInOutOfAura(Object entity) {
        if (entity instanceof LivingEntity) {
            return CloneEntityProtectionHandler.safePlayerCast((LivingEntity) entity);
        }
        return null;
    }
    
    /**
     * 拦截outOfEachEP方法中的Player类型转换
     */
    @Redirect(method = "outOfEachEP", 
              at = @At(value = "CHECKCAST", target = "net/minecraft/world/entity/player/Player"))
    private static Player redirectPlayerCastInOutOfEachEP(Object entity) {
        if (entity instanceof LivingEntity) {
            return CloneEntityProtectionHandler.safePlayerCast((LivingEntity) entity);
        }
        return null;
    }
    
    /**
     * 拦截getMP方法中的Player类型转换
     */
    @Redirect(method = "getMP", 
              at = @At(value = "CHECKCAST", target = "net/minecraft/world/entity/player/Player"))
    private static Player redirectPlayerCastInGetMP(Object entity) {
        if (entity instanceof LivingEntity) {
            return CloneEntityProtectionHandler.safePlayerCast((LivingEntity) entity);
        }
        return null;
    }
    
    /**
     * 拦截outOfMagicule方法中的Player类型转换
     */
    @Redirect(method = "outOfMagicule", 
              at = @At(value = "CHECKCAST", target = "net/minecraft/world/entity/player/Player"))
    private static Player redirectPlayerCastInOutOfMagicule(Object entity) {
        if (entity instanceof LivingEntity) {
            return CloneEntityProtectionHandler.safePlayerCast((LivingEntity) entity);
        }
        return null;
    }
}
