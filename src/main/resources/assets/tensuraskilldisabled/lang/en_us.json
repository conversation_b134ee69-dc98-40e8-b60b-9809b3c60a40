{"tensuraskilldisabled.config.title": "<PERSON><PERSON><PERSON> Disabled Configuration", "tensuraskilldisabled.config.disabled_skills": "Disabled Skills", "tensuraskilldisabled.config.disabled_skills.tooltip": "List of skills that should be disabled/prevented from being obtained", "tensuraskilldisabled.skill.blocked": "§cSkill acquisition blocked: %s", "tensuraskilldisabled.skill.blocked.tooltip": "This skill is in the blacklist and cannot be obtained", "tensuraskilldisabled.command.add.success": "Added skill '%s' to blacklist", "tensuraskilldisabled.command.remove.success": "Removed skill '%s' from blacklist", "tensuraskilldisabled.command.list.empty": "No skills are currently blacklisted", "tensuraskilldisabled.command.list.header": "Blacklisted skills (%d):", "tensuraskilldisabled.command.reload.success": "Skill blacklist configuration reloaded", "tensuraskilldisabled.command.invalid_id": "Invalid skill ID format. Use 'namespace:path' format", "tensuraskilldisabled.command.already_exists": "Skill '%s' is already in the blacklist", "tensuraskilldisabled.command.not_found": "Skill '%s' is not in the blacklist"}